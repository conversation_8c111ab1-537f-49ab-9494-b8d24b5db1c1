import { createClient } from "@/lib/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get("code");
  const next = searchParams.get("next") ?? "/dashboard";

  if (code) {
    const supabase = await createClient();
    
    const { error } = await supabase.auth.exchangeCodeForSession(code);
    
    if (!error) {
      // Successful OAuth login, redirect to dashboard or specified next URL
      return NextResponse.redirect(`${origin}${next}`);
    } else {
      // OAuth error, redirect to error page
      return NextResponse.redirect(`${origin}/auth/error?error=${error.message}`);
    }
  }

  // No code parameter, redirect to error page
  return NextResponse.redirect(`${origin}/auth/error?error=No authorization code provided`);
}
