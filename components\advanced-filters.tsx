"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { 
  Filter, 
  Calendar, 
  FileType, 
  HardDrive,
  User,
  X,
  RotateCcw
} from "lucide-react";

import { MediaCategory, MediaFilter } from "@/lib/types";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

interface AdvancedFiltersProps {
  currentFilter: MediaFilter;
  onFilterChange: (filter: MediaFilter) => void;
  availableTags: string[];
}

export function AdvancedFilters({ 
  currentFilter, 
  onFilterChange, 
  availableTags 
}: AdvancedFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [localFilter, setLocalFilter] = useState<MediaFilter>(currentFilter);

  const handleApplyFilters = () => {
    onFilterChange(localFilter);
    setIsOpen(false);
  };

  const handleResetFilters = () => {
    const resetFilter = {};
    setLocalFilter(resetFilter);
    onFilterChange(resetFilter);
  };

  const updateLocalFilter = (updates: Partial<MediaFilter>) => {
    setLocalFilter(prev => ({ ...prev, ...updates }));
  };

  const hasActiveFilters = Object.keys(currentFilter).length > 0;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          size="sm"
          className={cn(hasActiveFilters && "border-primary text-primary")}
        >
          <Filter className="h-4 w-4 mr-2" />
          Advanced Filters
          {hasActiveFilters && (
            <Badge variant="secondary" className="ml-2 text-xs">
              {Object.keys(currentFilter).length}
            </Badge>
          )}
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <span>Advanced Filters</span>
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleResetFilters}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset All
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Category Filter */}
          <div className="space-y-3">
            <Label className="flex items-center space-x-2">
              <FileType className="h-4 w-4" />
              <span>Category</span>
            </Label>
            <Select
              value={localFilter.category || "all"}
              onValueChange={(value) =>
                updateLocalFilter({ category: value === "all" ? undefined : value as MediaCategory })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="All categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All categories</SelectItem>
                <SelectItem value="2d_art">2D Art</SelectItem>
                <SelectItem value="3d_models">3D Models</SelectItem>
                <SelectItem value="textures_materials">Textures & Materials</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* File Size Filter */}
          <div className="space-y-3">
            <Label className="flex items-center space-x-2">
              <HardDrive className="h-4 w-4" />
              <span>File Size Range (MB)</span>
            </Label>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="min-size">Minimum</Label>
                  <Input
                    id="min-size"
                    type="number"
                    placeholder="0"
                    value={localFilter.minSize || ""}
                    onChange={(e) => 
                      updateLocalFilter({ 
                        minSize: e.target.value ? Number(e.target.value) : undefined 
                      })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max-size">Maximum</Label>
                  <Input
                    id="max-size"
                    type="number"
                    placeholder="100"
                    value={localFilter.maxSize || ""}
                    onChange={(e) => 
                      updateLocalFilter({ 
                        maxSize: e.target.value ? Number(e.target.value) : undefined 
                      })
                    }
                  />
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Date Range Filter */}
          <div className="space-y-3">
            <Label className="flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span>Upload Date</span>
            </Label>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="date-from">From</Label>
                <Input
                  id="date-from"
                  type="date"
                  value={localFilter.dateFrom || ""}
                  onChange={(e) => 
                    updateLocalFilter({ dateFrom: e.target.value || undefined })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="date-to">To</Label>
                <Input
                  id="date-to"
                  type="date"
                  value={localFilter.dateTo || ""}
                  onChange={(e) => 
                    updateLocalFilter({ dateTo: e.target.value || undefined })
                  }
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Tags Filter */}
          <div className="space-y-3">
            <Label>Tags</Label>
            <div className="space-y-2">
              <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                {availableTags.map((tag) => {
                  const isSelected = localFilter.tags?.includes(tag) || false;
                  return (
                    <Badge
                      key={tag}
                      variant={isSelected ? "default" : "outline"}
                      className="cursor-pointer hover:bg-secondary text-xs"
                      onClick={() => {
                        const currentTags = localFilter.tags || [];
                        const newTags = isSelected
                          ? currentTags.filter(t => t !== tag)
                          : [...currentTags, tag];
                        updateLocalFilter({ 
                          tags: newTags.length > 0 ? newTags : undefined 
                        });
                      }}
                    >
                      {tag}
                      {isSelected && (
                        <X className="h-3 w-3 ml-1" />
                      )}
                    </Badge>
                  );
                })}
              </div>
            </div>
          </div>

          <Separator />

          {/* Visibility Filter */}
          <div className="space-y-3">
            <Label>Visibility</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Switch
                  id="public-only"
                  checked={localFilter.isPublic === true}
                  onCheckedChange={(checked) => 
                    updateLocalFilter({ 
                      isPublic: checked ? true : undefined 
                    })
                  }
                />
                <Label htmlFor="public-only">Public assets only</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="private-only"
                  checked={localFilter.isPublic === false}
                  onCheckedChange={(checked) => 
                    updateLocalFilter({ 
                      isPublic: checked ? false : undefined 
                    })
                  }
                />
                <Label htmlFor="private-only">Private assets only</Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Sort Options */}
          <div className="space-y-3">
            <Label>Sort By</Label>
            <div className="grid grid-cols-2 gap-4">
              <Select
                value={localFilter.sortBy || "created_at"}
                onValueChange={(value) => 
                  updateLocalFilter({ sortBy: value as any })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_at">Upload Date</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="file_size">File Size</SelectItem>
                  <SelectItem value="updated_at">Last Modified</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={localFilter.sortOrder || "desc"}
                onValueChange={(value) => 
                  updateLocalFilter({ sortOrder: value as "asc" | "desc" })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="desc">Newest First</SelectItem>
                  <SelectItem value="asc">Oldest First</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
          >
            Cancel
          </Button>
          <Button onClick={handleApplyFilters}>
            Apply Filters
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
