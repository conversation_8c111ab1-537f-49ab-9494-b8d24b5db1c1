"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import {
  Menu,
  Upload,
  Users,
  MessageCircle,
  Settings,
  LogOut,
  Plus,
  Grid3X3,
  List,
  UserPlus,
  User,
  BarChart3
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ThemeSwitcher } from "@/components/theme-switcher";
import { LogoutButton } from "@/components/logout-button";
import { NotificationSystem } from "@/components/notification-system";
import { useUIStore } from "@/lib/store";
import { cn } from "@/lib/utils";

interface DashboardLayoutProps {
  children: React.ReactNode;
  user?: {
    email: string;
    display_name?: string;
    avatar_url?: string;
  };
}

const sidebarItems = [
  {
    icon: Grid3X3,
    label: "Media Grid",
    href: "/dashboard",
  },
  {
    icon: Upload,
    label: "Upload",
    action: "upload",
  },
  {
    icon: Users,
    label: "Groups",
    href: "/dashboard/groups",
  },
  {
    icon: UserPlus,
    label: "Friends",
    href: "/dashboard/friends",
  },
  {
    icon: MessageCircle,
    label: "Chat",
    href: "/dashboard/chat",
  },
  {
    icon: User,
    label: "Profile",
    href: "/dashboard/profile",
  },
  {
    icon: BarChart3,
    label: "Analytics",
    href: "/dashboard/analytics",
  },
  {
    icon: Settings,
    label: "Settings",
    href: "/dashboard/settings",
  },
];

export function DashboardLayout({ children, user }: DashboardLayoutProps) {
  const { sidebarOpen, setSidebarOpen, setUploadModalOpen } = useUIStore();
  const pathname = usePathname();

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile Header */}
      <div className="lg:hidden flex items-center justify-between p-4 border-b border-border/50 bg-background/80 backdrop-blur-md sticky top-0 z-40">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setSidebarOpen(!sidebarOpen)}
        >
          <Menu className="h-5 w-5" />
        </Button>
        
        <h1 className="font-semibold text-lg">ArtGrid</h1>

        <div className="flex items-center space-x-2">
          <NotificationSystem />
          <Button
            variant="default"
            size="icon"
            onClick={() => setUploadModalOpen(true)}
          >
            <Plus className="h-4 w-4" />
          </Button>
          {user && (
            <Link href="/dashboard/profile">
              <Avatar className="h-8 w-8">
                <AvatarImage src={user.avatar_url} />
                <AvatarFallback>
                  {user.display_name?.[0] || user.email[0].toUpperCase()}
                </AvatarFallback>
              </Avatar>
            </Link>
          )}
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <AnimatePresence>
          <motion.aside
            initial={{ x: -280 }}
            animate={{
              x: sidebarOpen ? 0 : -280
            }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className={cn(
              "fixed lg:static inset-y-0 left-0 z-50 w-70 bg-card/50 backdrop-blur-md border-r border-border/50",
              "lg:translate-x-0 lg:bg-card/30 lg:block sidebar-desktop",
              !sidebarOpen && "hidden lg:block"
            )}
          >
              <div className="flex flex-col h-full">
                {/* Logo */}
                <div className="p-6 border-b border-border/50">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/60 rounded-lg flex items-center justify-center">
                      <Grid3X3 className="h-4 w-4 text-primary-foreground" />
                    </div>
                    <div>
                      <h2 className="font-bold text-lg">ArtGrid</h2>
                      <p className="text-xs text-muted-foreground">Digital Asset Hub</p>
                    </div>
                  </div>
                </div>

                {/* Navigation */}
                <nav className="flex-1 p-4 space-y-2">
                  {sidebarItems.map((item) => (
                    <motion.div
                      key={item.href || item.action}
                      whileHover={{ x: 4 }}
                      transition={{ duration: 0.2 }}
                    >
                      {item.href ? (
                        <Link href={item.href}>
                          <Button
                            variant={pathname === item.href ? "secondary" : "ghost"}
                            className={cn(
                              "w-full justify-start h-11",
                              pathname === item.href && "bg-secondary/80"
                            )}
                          >
                            <item.icon className="mr-3 h-4 w-4" />
                            {item.label}
                          </Button>
                        </Link>
                      ) : (
                        <Button
                          variant="ghost"
                          className="w-full justify-start h-11"
                          onClick={() => {
                            if (item.action === "upload") setUploadModalOpen(true);
                          }}
                        >
                          <item.icon className="mr-3 h-4 w-4" />
                          {item.label}
                        </Button>
                      )}
                    </motion.div>
                  ))}
                </nav>

                <Separator className="mx-4" />

                {/* User Section */}
                <div className="p-4 space-y-4">
                  {/* Quick Upload */}
                  <Button
                    className="w-full"
                    onClick={() => setUploadModalOpen(true)}
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    Upload Media
                  </Button>

                  {/* User Info */}
                  {user && (
                    <Link href="/dashboard/profile">
                      <div className="flex items-center space-x-3 p-3 rounded-lg bg-muted/50 cursor-pointer hover:bg-muted/70 transition-colors">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={user.avatar_url} />
                          <AvatarFallback>
                            {user.display_name?.[0] || user.email[0].toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">
                            {user.display_name || user.email}
                          </p>
                          <p className="text-xs text-muted-foreground truncate">
                            {user.email}
                          </p>
                        </div>
                      </div>
                    </Link>
                  )}

                  {/* Footer Actions */}
                  <div className="flex items-center justify-between">
                    <ThemeSwitcher />
                    <LogoutButton />
                  </div>
                </div>
              </div>
            </motion.aside>
        </AnimatePresence>

        {/* Mobile Overlay */}
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Main Content */}
        <main className="flex-1 min-w-0">
          <div className="h-full">
            {children}
          </div>
        </main>
      </div>

    </div>
  );
}
