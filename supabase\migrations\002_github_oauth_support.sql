-- Update the handle_new_user function to support GitHub OAuth users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  github_username TEXT;
  github_name TEXT;
  github_avatar TEXT;
  final_username TEXT;
  final_display_name TEXT;
BEGIN
  -- Extract GitHub data if available
  github_username := NEW.raw_user_meta_data->>'user_name';
  github_name := NEW.raw_user_meta_data->>'full_name';
  github_avatar := NEW.raw_user_meta_data->>'avatar_url';
  
  -- Determine final username
  final_username := COALESCE(
    NEW.raw_user_meta_data->>'username',  -- From email signup
    github_username,                      -- From GitHub
    SPLIT_PART(NEW.email, '@', 1)        -- Fallback to email prefix
  );
  
  -- Determine final display name
  final_display_name := COALESCE(
    NEW.raw_user_meta_data->>'display_name', -- From email signup
    github_name,                              -- From GitHub
    final_username                            -- Fallback to username
  );
  
  -- Determine final avatar URL
  github_avatar := COALESCE(
    NEW.raw_user_meta_data->>'avatar_url', -- From email signup
    github_avatar                           -- From GitHub
  );
  
  INSERT INTO public.users (id, username, display_name, avatar_url)
  VALUES (
    NEW.id,
    final_username,
    final_display_name,
    github_avatar
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
